import 'package:flutter_audio_room/shared/constants/business_error_codes.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('BusinessErrorCodes', () {
    group('Token Refresh Codes', () {
      test('should identify token refresh codes correctly', () {
        expect(BusinessErrorCodes.isTokenRefreshCode('token.expired'), true);
        expect(BusinessErrorCodes.isTokenRefreshCode('token.invalid'), true);
        expect(BusinessErrorCodes.isTokenRefreshCode('access.token.replaced'), false);
        expect(BusinessErrorCodes.isTokenRefreshCode('user.banned'), false);
      });

      test('should contain correct token refresh codes', () {
        expect(BusinessErrorCodes.tokenRefreshCodes, {
          'token.expired',
          'token.invalid',
        });
      });
    });

    group('Logout Codes', () {
      test('should identify logout codes correctly', () {
        expect(BusinessErrorCodes.isLogoutCode('access.token.replaced'), true);
        expect(BusinessErrorCodes.isLogoutCode('token.expired'), false);
        expect(BusinessErrorCodes.isLogoutCode('user.banned'), false);
      });

      test('should contain correct logout codes', () {
        expect(BusinessErrorCodes.logoutCodes, {
          'access.token.replaced',
        });
      });
    });

    group('Restrict Codes', () {
      test('should identify restrict codes correctly', () {
        expect(BusinessErrorCodes.isRestrictCode('user.banned'), true);
        expect(BusinessErrorCodes.isRestrictCode('user.login.punished'), true);
        expect(BusinessErrorCodes.isRestrictCode('token.expired'), false);
        expect(BusinessErrorCodes.isRestrictCode('account.banned'), false);
      });

      test('should contain correct restrict codes', () {
        expect(BusinessErrorCodes.restrictCodes, {
          'user.banned',
          'user.login.punished',
        });
      });
    });

    group('Punishment Codes', () {
      test('should identify punishment codes correctly', () {
        expect(BusinessErrorCodes.isPunishmentCode('instant.voice.call.punishment'), true);
        expect(BusinessErrorCodes.isPunishmentCode('instant.chat.punishment'), true);
        expect(BusinessErrorCodes.isPunishmentCode('audio.room.create.punishment'), true);
        expect(BusinessErrorCodes.isPunishmentCode('audio.room.join.punishment'), true);
        expect(BusinessErrorCodes.isPunishmentCode('token.expired'), false);
      });

      test('should contain correct punishment codes', () {
        expect(BusinessErrorCodes.punishmentCodes, {
          'instant.voice.call.punishment',
          'instant.chat.punishment',
          'audio.room.create.punishment',
          'audio.room.join.punishment',
        });
      });
    });

    group('Non-Retryable Business Codes', () {
      test('should identify non-retryable business codes correctly', () {
        // 认证相关
        expect(BusinessErrorCodes.isNonRetryableBusinessCode('access.token.replaced'), true);
        expect(BusinessErrorCodes.isNonRetryableBusinessCode('token.expired'), true);
        expect(BusinessErrorCodes.isNonRetryableBusinessCode('token.invalid'), true);
        
        // 账户限制相关
        expect(BusinessErrorCodes.isNonRetryableBusinessCode('account.banned'), true);
        expect(BusinessErrorCodes.isNonRetryableBusinessCode('account.restricted'), true);
        expect(BusinessErrorCodes.isNonRetryableBusinessCode('user.banned'), true);
        expect(BusinessErrorCodes.isNonRetryableBusinessCode('user.login.punished'), true);
        
        // 权限相关
        expect(BusinessErrorCodes.isNonRetryableBusinessCode('permission.denied'), true);
        expect(BusinessErrorCodes.isNonRetryableBusinessCode('validation.failed'), true);
        
        // 惩罚相关
        expect(BusinessErrorCodes.isNonRetryableBusinessCode('instant.voice.call.punishment'), true);
        expect(BusinessErrorCodes.isNonRetryableBusinessCode('instant.chat.punishment'), true);
        expect(BusinessErrorCodes.isNonRetryableBusinessCode('audio.room.create.punishment'), true);
        expect(BusinessErrorCodes.isNonRetryableBusinessCode('audio.room.join.punishment'), true);
        
        // 其他错误码应该返回false
        expect(BusinessErrorCodes.isNonRetryableBusinessCode('unknown.error'), false);
        expect(BusinessErrorCodes.isNonRetryableBusinessCode('network.timeout'), false);
      });
    });

    group('Error Descriptions', () {
      test('should return correct descriptions for known codes', () {
        expect(BusinessErrorCodes.getErrorDescription('token.expired'),
            'Login expired, please login again');
        expect(BusinessErrorCodes.getErrorDescription('token.invalid'),
            'Access info invalid, please login again');
        expect(BusinessErrorCodes.getErrorDescription('access.token.replaced'),
            'Account logged in on other devices, current session invalid');
        expect(BusinessErrorCodes.getErrorDescription('account.banned'),
            'Account banned');
        expect(BusinessErrorCodes.getErrorDescription('user.banned'),
            'User banned');
        expect(BusinessErrorCodes.getErrorDescription('permission.denied'),
            'Permission denied');
        expect(
            BusinessErrorCodes.getErrorDescription(
                'instant.voice.call.punishment'),
            'Instant voice call punished');
      });

      test('should return default description for unknown codes', () {
        expect(BusinessErrorCodes.getErrorDescription('unknown.error'),
            'Unknown error: unknown.error');
        expect(BusinessErrorCodes.getErrorDescription('custom.error'),
            'Unknown error: custom.error');
      });
    });

    group('Constants Values', () {
      test('should have correct constant values', () {
        expect(BusinessErrorCodes.tokenExpired, 'token.expired');
        expect(BusinessErrorCodes.tokenInvalid, 'token.invalid');
        expect(BusinessErrorCodes.accessTokenReplaced, 'access.token.replaced');
        expect(BusinessErrorCodes.accountBanned, 'account.banned');
        expect(BusinessErrorCodes.accountRestricted, 'account.restricted');
        expect(BusinessErrorCodes.userBanned, 'user.banned');
        expect(BusinessErrorCodes.userLoginPunished, 'user.login.punished');
        expect(BusinessErrorCodes.permissionDenied, 'permission.denied');
        expect(BusinessErrorCodes.validationFailed, 'validation.failed');
        expect(BusinessErrorCodes.instantVoiceCallPunishment, 'instant.voice.call.punishment');
        expect(BusinessErrorCodes.instantChatPunishment, 'instant.chat.punishment');
        expect(BusinessErrorCodes.audioRoomCreatePunishment, 'audio.room.create.punishment');
        expect(BusinessErrorCodes.audioRoomJoinPunishment, 'audio.room.join.punishment');
      });
    });

    group('Code Sets Integrity', () {
      test('should not have overlapping codes between different categories', () {
        // Token refresh codes 和 logout codes 不应该重叠
        final tokenRefreshAndLogout = BusinessErrorCodes.tokenRefreshCodes
            .intersection(BusinessErrorCodes.logoutCodes);
        expect(tokenRefreshAndLogout.isEmpty, true);

        // Restrict codes 和 punishment codes 不应该重叠
        final restrictAndPunishment = BusinessErrorCodes.restrictCodes
            .intersection(BusinessErrorCodes.punishmentCodes);
        expect(restrictAndPunishment.isEmpty, true);
      });

      test('should include all specific codes in non-retryable set', () {
        // 所有的 token refresh codes 都应该在 non-retryable 中
        for (final code in BusinessErrorCodes.tokenRefreshCodes) {
          expect(BusinessErrorCodes.nonRetryableBusinessCodes.contains(code), true,
                 reason: 'Token refresh code $code should be non-retryable');
        }

        // 所有的 logout codes 都应该在 non-retryable 中
        for (final code in BusinessErrorCodes.logoutCodes) {
          expect(BusinessErrorCodes.nonRetryableBusinessCodes.contains(code), true,
                 reason: 'Logout code $code should be non-retryable');
        }

        // 所有的 restrict codes 都应该在 non-retryable 中
        for (final code in BusinessErrorCodes.restrictCodes) {
          expect(BusinessErrorCodes.nonRetryableBusinessCodes.contains(code), true,
                 reason: 'Restrict code $code should be non-retryable');
        }

        // 所有的 punishment codes 都应该在 non-retryable 中
        for (final code in BusinessErrorCodes.punishmentCodes) {
          expect(BusinessErrorCodes.nonRetryableBusinessCodes.contains(code), true,
                 reason: 'Punishment code $code should be non-retryable');
        }
      });
    });
  });
}
