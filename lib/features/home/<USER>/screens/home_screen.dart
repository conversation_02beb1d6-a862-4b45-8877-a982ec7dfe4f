import 'dart:async';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/theme/text_theme.dart';
import 'package:flutter_audio_room/core/utils/app_lifecycle.dart'
    as app_lifecycle;
import 'package:flutter_audio_room/core/utils/firebase_message_utils.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/local_notification_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/core/widgets/app_scaffold.dart';
import 'package:flutter_audio_room/features/app_settings/screen/app_settings_screen.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_join_source.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_mini_player_service.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/leave_room_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/lucky_draw_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/screens/audio_room_home_screen.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/screens/audio_room_screen.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/chat/presentation/controllers/chat_socket_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/pages/conversation_list_page.dart';
import 'package:flutter_audio_room/features/instant_chat/domain/entities/call_status.dart';
import 'package:flutter_audio_room/features/instant_chat/presentation/provider/instant_call_provider.dart';
import 'package:flutter_audio_room/features/leaderboard/screen/leaderboard_screen.dart';
import 'package:flutter_audio_room/features/my/presentation/screens/my_home_screen.dart';
import 'package:flutter_audio_room/features/purchase/presentation/pages/shop_page.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/voice_call_state.dart';
import 'package:flutter_audio_room/features/voice_call/friend/provider/friend_voice_call_provider.dart';
import 'package:flutter_audio_room/features/voice_call/friend/screen/friend_voice_call_screen.dart';
import 'package:flutter_audio_room/gen/assets.gen.dart';
import 'package:flutter_audio_room/services/core_service/core_service.dart';
import 'package:flutter_audio_room/services/core_service/core_service_provider.dart';
import 'package:flutter_audio_room/services/gift_service/presentation/provider/gift_provider.dart';
import 'package:flutter_audio_room/services/location_service/i_location_service.dart';
import 'package:flutter_audio_room/services/network_info_service/i_network_info_service.dart';
import 'package:flutter_audio_room/services/network_info_service/network_info_service.dart';
import 'package:flutter_audio_room/services/websocket_service/data/websocket_datasource_manager.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../providers/home_provider.dart';

enum HomeTabName {
  home,
  shop,
  leaderboard,
  chat,
  my,
}

class HomePagesOption {
  final Widget page;
  final SvgGenImage icon;
  final HomeTabName homeTabName;
  AppBar? appBar;

  HomePagesOption({
    required this.page,
    required this.icon,
    required this.homeTabName,
    this.appBar,
  });
}

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> with LeaveRoomMixin {
  // 用于标识生命周期回调的键
  static const String _lifecycleCallbackKey = 'home_screen';

  bool _notificationAuthorized = false;

  StreamSubscription<String>? _tokenRefreshSubscription;
  StreamSubscription<RemoteMessage>? _messageReceivedSubscription;

  @override
  void initState() {
    super.initState();

    // 注册应用生命周期状态变化回调
    app_lifecycle.registerLifecycleCallback(
      _lifecycleCallbackKey,
      (context) {
        // 当应用从后台恢复到前台时
        if (context.currentState == AppLifecycleState.resumed &&
            context.isResumingFromBackground) {
          ref.read(homeProvider.notifier).setIsInBackground(false);
          // 使用原子操作重置WebSocket重连状态并检查连接，避免竞态条件
          getIt<WebSocketDataSourceManager>().resetAndCheckAllConnections();
          _clearNotificationAndBadge();
        }
        // 只有当应用进入真正的后台状态时才设置isInBackground为true
        else if (context.currentState == AppLifecycleState.paused) {
          ref.read(homeProvider.notifier).setIsInBackground(true);
        }
      },
    );

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      if (!mounted) {
        return;
      }
      final userInfo =
          ref.read(accountProvider.select((state) => state.userInfo));
      final hasUser = userInfo?.sessionInfo != null;
      if (!hasUser) {
        return;
      }
      _checkAndBackToRoom();

      getIt<CoreService>().init();

      _initFirebaseMessage();

      _updateInfo();
      _initFriendWebSocket();
      _getMatchRemainQuota();
      _initializeGiftList();
      _initUserData();
    });
  }

  @override
  void dispose() {
    _tokenRefreshSubscription?.cancel();
    _messageReceivedSubscription?.cancel();
    app_lifecycle.unregisterLifecycleCallback(_lifecycleCallbackKey);
    super.dispose();
  }

  Future<void> _initFirebaseMessage() async {
    final authorized = await FirebaseMessageUtils.requestPermission();
    if (!authorized) return;

    _notificationAuthorized = authorized;

    final token = await FirebaseMessageUtils.getToken();
    LogUtils.i('FirebaseMessageUtils.getToken: $token', tag: 'App');
    if (!mounted) return;
    ref.read(coreServiceProvider.notifier).updateFcmToken(token);

    _tokenRefreshSubscription =
        FirebaseMessageUtils.tokenRefreshStream?.listen((token) {
      LogUtils.i('FirebaseMessageUtils.tokenRefreshSubscription: $token',
          tag: 'App');
      ref.read(coreServiceProvider.notifier).updateFcmToken(token);
    });

    _clearNotificationAndBadge();

    _setupInteractedMessage();
  }

  void _clearNotificationAndBadge() {
    if (!_notificationAuthorized) return;
    NotificationService.clearAllNotificationsAndBadges();

    if (!mounted) return;
    ref.read(coreServiceProvider.notifier).clearNotificationCount();
  }

  Future<void> _setupInteractedMessage() async {
    final message = await FirebaseMessageUtils.getInitialMessage();
    if (message != null) {
      _handleInteractedMessage(message);
    }

    _messageReceivedSubscription =
        FirebaseMessageUtils.messageReceivedStream?.listen((message) {
      _handleInteractedMessage(message);
    });
  }

  void _handleInteractedMessage(RemoteMessage message) {
    LogUtils.i(
      'FirebaseMessageUtils._handleInteractedMessage: ${message.toMap()}',
      tag: 'App',
    );
  }

  Future<void> _getMatchRemainQuota() async {
    final coreServiceNotifier = ref.read(coreServiceProvider.notifier);
    await coreServiceNotifier.getMatchRemainQuota();
  }

  Future<void> _checkAndBackToRoom() async {
    final user = ref.read(accountProvider.select((state) => state.userInfo));
    final audioRoomNotifier = ref.read(audioRoomProvider.notifier);
    await audioRoomNotifier.initialize();

    final roomInfo = audioRoomNotifier.checkLocalRoomInfo();
    if (roomInfo != null && user != null) {
      await LoadingUtils.showLoading();
      final result = await audioRoomNotifier.joinRoom(
        user: user,
        roomId: roomInfo.id ?? '',
        joinSource: RoomJoinSource.creator_reconnect,
      );
      result.fold(
        (failure) => LoadingUtils.showError(failure.message),
        (response) async {
          await LoadingUtils.dismiss();
          if (mounted) {
            context.push(
              RoutePageConfig(route: AudioRoomScreen.route()),
            );
          }
        },
      );
    }
  }

  Future<void> _initializeGiftList() async {
    _initGiftList();
    _initLuckyDrawList();
  }

  Future<void> _initGiftList() async {
    final giftStateNotifier = ref.read(giftStateNotifierProvider.notifier);
    await giftStateNotifier.refreshGiftList();
    await giftStateNotifier.refreshFrameList();
    await giftStateNotifier.refreshBagFrameList();
    await giftStateNotifier.getReceivedGiftHistory(
          current: 1,
          size: 10,
        );
    await giftStateNotifier.getSentGiftHistory(
          current: 1,
          size: 10,
        );
  }

  Future<void> _initLuckyDrawList() async {
    final luckyDrawNotifier = ref.read(luckyDrawProvider.notifier);
    await luckyDrawNotifier.fetchDrawItems();
  }

  Future<void> _initFriendWebSocket() async {
    final notifier = ref.read(friendVoiceCallProvider.notifier);
    await notifier.initializeWebSocket();
  }

  Future<void> _updateInfo() async {
    _updateLocation();
    _updateIpAddress();
  }

  Future<void> _updateIpAddress() async {
    final accountNotifier = ref.read(accountProvider.notifier);
    final result = await getIt<INetworkInfoService>().getPublicIpAddress();
    result.fold(
      (error) => LogUtils.e('Update ipAddress error: $error', tag: 'App'),
      (ipAddress) {
        if (!mounted) return;
        accountNotifier.updateIpAddress(ipAddress: ipAddress);
      },
    );
  }

  Future<void> _updateLocation() async {
    final accountNotifier = ref.read(accountProvider.notifier);
    final result = await getIt<ILocationService>().getCurrentLocation();
    result.fold(
      (error) => LogUtils.e('location: $error', tag: 'App'),
      (location) {
        accountNotifier.updateLocation(
          countryCode: location.countryCode,
          cityName: location.cityName,
          latitude: location.latitude,
          longitude: location.longitude,
        );
      },
    );
  }

  Future<void> _initUserData() async {
    final accountNotifier = ref.read(accountProvider.notifier);
    try {
      // Fetch subscription info and personal info
      await accountNotifier.refreshUserData();
    } catch (e) {
      LogUtils.e('Failed to initialize user data: $e', tag: 'HomeScreen');
    }
  }

  Future<void> _handCallIn(VoiceCallState call) async {
    final voiceCallNotifier = ref.read(friendVoiceCallProvider.notifier);
    final instantCallNotifier = ref.read(instantCallProvider.notifier);

    final inAudioRoom = ref
        .read(audioRoomProvider.select((state) => state.currentRoom != null));
    final isInInstantCall =
        ref.read(instantCallProvider.select((state) => state.isCallActive));
    final isInFriendCall =
        ref.read(friendVoiceCallProvider.select((state) => state.isCallActive));
    if (inAudioRoom) {
      final res = await context.showOkCancelAlertDialog(
        title: 'Incoming Call',
        content:
            'Do you want to answer the call? It will quit audio room and join call.',
      );
      if (res == false) {
        voiceCallNotifier.rejectCall();
      } else {
        await leaveRoom();
        if (!mounted) return;
        context.popUntilFirst();
      }
    } else if (isInInstantCall) {
      final res = await context.showOkCancelAlertDialog(
        title: 'Incoming Call',
        content:
            'Do you want to answer the call? It will quit the current call and join the new one.',
      );

      if (res == false) {
        voiceCallNotifier.rejectCall();
      } else {
        instantCallNotifier.endCall();
      }
    } else if (isInFriendCall) {
      final res = await context.showOkCancelAlertDialog(
        title: 'Incoming Call',
        content:
            'Do you want to answer the call? It will quit the current call and join the new one.',
      );

      if (res == false) {
        voiceCallNotifier.rejectCall();
      } else {
        voiceCallNotifier.endCall();
      }
    }

    if (!mounted) return;
    context.push(
      RoutePageConfig(
        route: FriendVoiceCallScreen.route(
          friendId: call.peerId!,
          friendName: call.peerNickName,
          friendAvatar: '',
          fromMiniPlayer: false,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentIndex = ref.watch(homeProvider).currentIndex;

    ref.listen(homeProvider.select((state) => state.messages),
        (previous, next) {
      if (next.isNotEmpty) {
        final message = next.last;
        LoadingUtils.showToast(message.data);
      }
    });

    ref.listen(
      networkConnectionStateProvider,
      (previous, next) {
        if (previous?.value == null) {
          return;
        } else if (previous?.value != next.value) {
          next.whenData(
            (isConnected) {
              final wasConnected = previous?.value?.getRight() ?? false;
              final isNowConnected = isConnected.getRight() ?? false;

              // 当网络从断开恢复到连接时，重置WebSocket重连状态
              if (!wasConnected && isNowConnected) {
                LogUtils.i(
                  'Network restored, resetting WebSocket reconnection states',
                  tag: 'HomeScreen.networkListener',
                );
                // 使用原子操作重置WebSocket重连状态并检查连接，避免竞态条件
                getIt<WebSocketDataSourceManager>()
                    .resetAndCheckAllConnections();
              }

              // if (isConnected.getRight() != true) {
              //   context.showSnackBar(context.l10n.noNetwork);
              // } else {
              //   context.showSnackBar('Network connected');
              // }
            },
          );
        }
      },
    );

    ref.listen(friendVoiceCallProvider, (previous, next) {
      if (previous?.callId != next.callId && next.callId != null) {
        if (next.peerId == null) {
          return;
        }

        _handCallIn(next);
      } else if (previous?.callStatus is! CallDisconnected &&
          next.callStatus is CallDisconnected) {
        getIt<IMiniPlayerService>().removeMiniPlayer();
      }
    }, onError: (error, stackTrace) {
      LogUtils.e(error.toString(), error: stackTrace, tag: 'HomeScreen');
    });

    ref.listen(instantCallProvider, (previous, next) {
      if (previous?.callStatus is! CallDisconnected &&
          next.callStatus is CallDisconnected) {
        getIt<IMiniPlayerService>().removeMiniPlayer();
      }
    });

    final pages = _createPages(currentIndex);

    return Stack(
      children: [
        Positioned.fill(
          child: AppScaffold(
            contentPadding: EdgeInsets.zero,
            resizeToAvoidBottomInset: false,
            appBar: pages[currentIndex].appBar,
            body: IndexedStack(
              index: currentIndex,
              sizing: StackFit.expand,
              children: pages.map((e) => e.page).toList(),
            ),
            bottomNavigationBar: _buildBottomBar(currentIndex, pages),
          ),
        ),
        // _buildOverlay(),
      ],
    );
  }

  List<HomePagesOption> _createPages(int currentIndex) {
    return [
      HomePagesOption(
        page: const AudioRoomHomeScreen(),
        icon: Assets.svgs.tabHome,
        homeTabName: HomeTabName.home,
      ),
      HomePagesOption(
        page: const ShopPage(),
        icon: Assets.svgs.tabShop,
        homeTabName: HomeTabName.shop,
      ),
      HomePagesOption(
        page: const LeaderboardScreen(),
        icon: Assets.svgs.tabLeaderboard,
        homeTabName: HomeTabName.leaderboard,
      ),
      HomePagesOption(
        page: const ConversationListPage(),
        icon: Assets.svgs.tabFollowing,
        homeTabName: HomeTabName.chat,
        appBar: AppBar(
          automaticallyImplyLeading: false,
          title: Builder(builder: (builder) {
            final socketIoState = ref.watch(chatSocketProvider);
            if (socketIoState != SocketConnectionState.connected) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('Connecting...'),
                  5.horizontalSpace,
                  SizedBox(
                    width: 16.w,
                    height: 16.w,
                    child: CircularProgressIndicator(
                      color: context.colorScheme.primary,
                    ),
                  ),
                ],
              );
            }
            return const Text('Chat');
          }),
        ),
      ),
      HomePagesOption(
        page: const MyHomeScreen(),
        icon: Assets.svgs.tabMe,
        homeTabName: HomeTabName.my,
        appBar: AppBar(
          centerTitle: false,
          automaticallyImplyLeading: false,
          title: Row(
            children: [
              12.horizontalSpace,
              AppButton(
                type: AppButtonType.contained,
                padding: EdgeInsets.zero,
                onPressed: () {
                  ref.read(homeProvider.notifier).setTab(1);
                  ref.read(shopTabProvider.notifier).state =
                      ShopTabType.premium;
                },
                child: Container(
                  height: 24.h,
                  padding: EdgeInsets.only(
                    right: 10.w,
                    left: 3.w,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(999.r),
                    border: Border.all(
                      color: context.colorScheme.outline,
                    ),
                    gradient: const LinearGradient(
                      colors: [
                        Color(0xff262626),
                        Color(0xff201F1F),
                      ],
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Assets.images.proCrown.image(
                        width: 30.w,
                        height: 30.w,
                      ),
                      Text(
                        'Premium',
                        style: context.textTheme.bodySmallMedium.copyWith(
                          fontSize: 10.sp,
                          color: const Color(0xffF7C906),
                        ),
                      ),
                    ],
                  ),
                ),
                textStyle: (context, style) => style.copyWith(
                  color: context.colorScheme.primary,
                ),
              ),
            ],
          ),
          actions: [
            IconButton(
              onPressed: () {
                context.push(
                  const WidgetPageConfig(
                    page: AppSettingsScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.settings),
            ),
          ],
        ),
      ),
    ];
  }

  Widget _buildBottomBar(int currentIndex, List<HomePagesOption> pages) {
    List<BottomNavigationBarItem> items = [];

    for (var i = 0; i < pages.length; i++) {
      final e = pages[i];
      items.add(
        BottomNavigationBarItem(
          icon: e.icon.svg(
            // ignore: deprecated_member_use_from_same_package
            color: currentIndex == i ? context.colorScheme.primary : null,
          ),
          label: e.homeTabName.name,
        ),
      );
    }

    return SafeArea(
      minimum: EdgeInsets.only(bottom: 20.h),
      child: Container(
        height: 66.h,
        margin: EdgeInsets.symmetric(horizontal: 10.w),
        decoration: BoxDecoration(
          color: const Color(0xff232323),
          borderRadius: BorderRadius.circular(999.r),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(999.r),
          child: Theme(
            data: ThemeData(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
            ),
            child: BottomNavigationBar(
              currentIndex: currentIndex,
              onTap: (index) {
                ref.read(homeProvider.notifier).setTab(index);
              },
              selectedItemColor: context.colorScheme.primary,
              unselectedItemColor: CupertinoColors.systemGrey,
              backgroundColor: const Color(0xff232323),
              type: BottomNavigationBarType.fixed,
              showSelectedLabels: true,
              selectedFontSize: 10.sp,
              unselectedFontSize: 10.sp,
              selectedLabelStyle: context.textTheme.bodyMediumSemiBold.copyWith(
                color: context.colorScheme.primary,
                fontSize: 10.sp,
              ),
              unselectedLabelStyle:
                  context.textTheme.bodyMediumSemiBold.copyWith(
                color: CupertinoColors.systemGrey,
                fontSize: 10.sp,
              ),
              showUnselectedLabels: true,
              items: items,
            ),
          ),
        ),
      ),
    );
  }

  // Widget _buildOverlay() {
  //   return Positioned.fill(
  //     child: IgnorePointer(
  //       child: AnimatedOpacity(
  //         opacity:
  //             ref.watch(homeProvider.select((state) => state.isInBackground))
  //                 ? 1.0
  //                 : 0.0,
  //         duration: const Duration(milliseconds: 500),
  //         child: Container(
  //           color: context.colorScheme.surface,
  //           child: const Center(
  //             child: Icon(Icons.lock),
  //           ),
  //         ),
  //       ),
  //     ),
  //   );
  // }
}
