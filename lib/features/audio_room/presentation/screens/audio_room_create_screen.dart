import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/localization_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_category.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_info.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/screens/audio_room_screen.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/services/punishment_service/model/punishment_exception.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:uuid/uuid.dart';

class AudioRoomCreateScreen extends ConsumerStatefulWidget {
  const AudioRoomCreateScreen({super.key, required this.quotaCount});

  final int quotaCount;

  @override
  ConsumerState<AudioRoomCreateScreen> createState() =>
      _AudioRoomCreateScreenState();
}

class _AudioRoomCreateScreenState extends ConsumerState<AudioRoomCreateScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _announcementController = TextEditingController();
  RoomCategory _selectedCategory = RoomCategory.values.first;
  late String _deduplicateKey;
  bool _isPrivateRoom = false;
  final String _selectedBackground = '000000';

  // 房间分类选项
  final List<RoomCategory> _roomCategories = RoomCategory.values;

  @override
  void initState() {
    super.initState();
    _deduplicateKey = const Uuid().v4();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _announcementController.dispose();
    super.dispose();
  }

  Future<void> _createAudioRoom() async {
    final form = _formKey.currentState;
    if (form == null || !form.validate()) return;
    final title = _titleController.text;
    final announcement = _announcementController.text;
    final category = _selectedCategory;
    final background = _selectedBackground;
    final type = _isPrivateRoom ? RoomType.private : RoomType.public;

    final user = ref.read(accountProvider).userInfo;
    if (user == null) {
      LoadingUtils.showError(context.l10n.userNotFound);
      return;
    }

    await LoadingUtils.showLoading();

    final result =
        await ref.read(audioRoomProvider.notifier).createAndJoinAudioRoom(
              user: user,
              title: title,
              category: category,
              announcement: announcement,
              background: background,
              type: type,
              deduplicateKey: _deduplicateKey,
            );
    result.fold(
      (failure) {
        if (failure is PunishmentException) {
          LoadingUtils.dismiss();
          context.showPunishmentDialog(failure);
        } else {
          LoadingUtils.showError(failure.message);
        }
      },
      (response) async {
        await Future.delayed(const Duration(milliseconds: 300));
        await LoadingUtils.dismiss();
        if (mounted) {
          context.pushReplacement(
            RoutePageConfig(route: AudioRoomScreen.route()),
          );
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: EdgeInsets.only(bottom: 10.h),
      child: Stack(
        children: [
          Positioned.fill(
            child: Scaffold(
              appBar: AppBar(
                title: Text(
                  context.l10n.createAudioRoom,
                ),
              ),
              body: SingleChildScrollView(
                padding: EdgeInsets.zero,
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        child: _buildSectionTitle('Type'),
                      ),
                      3.verticalSpace,
                      _buildCategorySelector(),
                      32.verticalSpace,
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildSectionTitle('Name'),
                            _buildTitleInput(),
                            32.verticalSpace,
                            _buildSectionTitle(context.l10n.announcement),
                            _buildAnnouncementInput(),
                            32.verticalSpace,
                            _buildPrivateRoomToggle(),
                            32.verticalSpace,
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            bottom: context.mediaQuery.viewPadding.bottom,
            left: 0,
            right: 0,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: AppButton(
                    onPressed: _createAudioRoom,
                    height: 50.h,
                    width: double.infinity,
                    borderRadius: 99,
                    padding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    text: context.l10n.create,
                  ),
                ),
                16.verticalSpace,
                _buildRemainingCount(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18.sp,
        fontWeight: FontWeight.bold,
        color: context.colorScheme.onSurface.withValues(alpha: 0.6),
      ),
    );
  }

  Widget _buildCategorySelector() {
    return SizedBox(
      height: 70.h,
      child: MasonryGridView.count(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        scrollDirection: Axis.horizontal,
        crossAxisCount: 2,
        mainAxisSpacing: 8.w,
        crossAxisSpacing: 8.h,
        itemCount: _roomCategories.length,
        itemBuilder: (context, index) {
          final category = _roomCategories[index];
          final isSelected = _selectedCategory == category;
          return InkWell(
            onTap: () {
              setState(() {
                _selectedCategory = category;
              });
            },
            borderRadius: BorderRadius.circular(999),
            child: Container(
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24.r),
                border: Border.all(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Colors.grey[300]!,
                  width: 1,
                ),
              ),
              child: Text(
                category.name,
                style: TextStyle(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : context.theme.colorScheme.onSurface,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTitleInput() {
    final baseBorder = OutlineInputBorder(
      borderSide: const BorderSide(
        color: Color(0xFF49494B),
        width: 1,
      ),
      borderRadius: BorderRadius.circular(6.r),
    );
    return TextFormField(
      controller: _titleController,
      textInputAction: TextInputAction.next,
      decoration: InputDecoration(
        filled: true,
        hintText: 'Create a cool room name',
        hintStyle: TextStyle(
          color: context.colorScheme.onSurface.withValues(alpha: 0.5),
          fontSize: 16.sp,
        ),
        border: baseBorder,
        enabledBorder: baseBorder,
        errorBorder: baseBorder.copyWith(
          borderSide: baseBorder.borderSide.copyWith(
            color: context.colorScheme.error,
            width: baseBorder.borderSide.width,
          ),
        ),
        focusedBorder: baseBorder.copyWith(
          borderSide: baseBorder.borderSide.copyWith(
            color: Theme.of(context).colorScheme.primary,
            width: baseBorder.borderSide.width,
          ),
        ),
        contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      ),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return context.l10n.errorRequiredField;
        }
        if (value.length > 50) {
          return 'Cannot exceed 50 characters'; // TODO: add to l10n if needed
        }
        return null;
      },
    );
  }

  Widget _buildAnnouncementInput() {
    final baseBorder = OutlineInputBorder(
      borderSide: const BorderSide(
        color: Color(0xFF49494B),
        width: 1,
      ),
      borderRadius: BorderRadius.circular(6.r),
    );
    return TextFormField(
      controller: _announcementController,
      textInputAction: TextInputAction.done,
      decoration: InputDecoration(
        filled: true,
        hintText: 'What would you like to announce to each participant?',
        hintStyle: TextStyle(
          color: context.colorScheme.onSurface.withValues(alpha: 0.5),
          fontSize: 16.sp,
        ),
        border: baseBorder,
        enabledBorder: baseBorder,
        focusedBorder: baseBorder.copyWith(
          borderSide: baseBorder.borderSide.copyWith(
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        errorBorder: baseBorder.copyWith(
          borderSide: baseBorder.borderSide.copyWith(
            color: context.colorScheme.error,
          ),
        ),
        contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      ),
      maxLines: 2,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return context.l10n.errorRequiredField;
        }
        if (value.length > 200) {
          return 'Cannot exceed 200 characters'; // TODO: add to l10n if needed
        }
        return null;
      },
    );
  }

  Widget _buildPrivateRoomToggle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Private Room',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: context.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            4.verticalSpace,
            Text(
              'Only invited users can join',
              style: TextStyle(
                color: context.colorScheme.onSurface.withValues(alpha: 0.4),
                fontSize: 14.sp,
              ),
            ),
          ],
        ),
        Switch(
          value: _isPrivateRoom,
          onChanged: (value) {
            setState(() {
              _isPrivateRoom = value;
            });
          },
          activeColor: Theme.of(context).colorScheme.onSurface,
        ),
      ],
    );
  }

  Widget _buildRemainingCount() {
    return Container(
      width: double.infinity,
      alignment: Alignment.center,
      child: Text(
        '${widget.quotaCount} remaining rooms left for today',
        style: TextStyle(
          color: Colors.grey,
          fontSize: 14.sp,
        ),
      ),
    );
  }
}
