import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/router/app_router.dart';
import 'package:flutter_audio_room/core/theme/text_theme.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/localization_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/core/widgets/avatar_with_frame.dart';
import 'package:flutter_audio_room/features/app_settings/mixin/block_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/other_user_info_model.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/tap_user_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/screens/report_user_screen.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/profile_provider.dart';
import 'package:flutter_audio_room/features/chat/data/model/user_online_status_model.dart';
import 'package:flutter_audio_room/features/chat/data/model/user_room_status_model.dart';
import 'package:flutter_audio_room/features/chat/domain/models/message_config.dart';
import 'package:flutter_audio_room/features/chat/presentation/controllers/chat_socket_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/pages/mixin/chat_page_socket_mixin.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/conversation_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/custom_chat.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/ephemeral_setting.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/live_tag.dart';
import 'package:flutter_audio_room/features/my/domain/entities/follow_origin.dart';
import 'package:flutter_audio_room/features/my/presentation/provider/follow_provider.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/widgets/app_scaffold.dart';
import '../providers/chat_provider.dart';

class ChatPage extends ConsumerStatefulWidget {
  const ChatPage({
    super.key,
    required this.conversationId,
    required this.peer,
    required this.userRoomStatus,
    required this.userOnlineStatus,
  });

  final String conversationId;
  final types.User peer;
  final UserRoomStatusModel? userRoomStatus;
  final UserOnlineStatusModel? userOnlineStatus;

  static const String routeName = 'chat_page';

  static Route<ChatPage> route(
    String conversationId,
    types.User peer,
    UserRoomStatusModel? userRoomStatus,
    UserOnlineStatusModel? userOnlineStatus,
  ) {
    return MaterialPageRoute(
      builder: (context) => ChatPage(
        conversationId: conversationId,
        peer: peer,
        userRoomStatus: userRoomStatus,
        userOnlineStatus: userOnlineStatus,
      ),
      settings: const RouteSettings(
        name: routeName,
      ),
    );
  }

  @override
  ConsumerState<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends ConsumerState<ChatPage>
    with ChatInputStatusListenerMixin, RouteAware, BlockMixin, TapUserMixin {
  types.User get currentUser {
    if (!mounted) {
      return const types.User(id: '');
    }

    final profile = ref.read(accountProvider).userInfo?.profile;
    return types.User(
      id: profile?.id ?? '',
      firstName: profile?.nickName ?? 'Unknown',
    );
  }

  bool _isTyping = false;
  Timer? _typingTimer;
  Timer? _messageExpirationTimer;

  OtherUserInfoModel? _otherUser;

  late Conversation _conversationNotifier;
  late ChatNotifier _chatNotifier;
  late ChatSocket _chatSocketNotifier;

  @override
  dynamic typingStatusListener(String senderId, bool typing) async {
    if (senderId != widget.conversationId) {
      return null;
    }

    if (mounted) {
      setState(() {
        _isTyping = typing;
        if (typing) {
          _typingTimer?.cancel();
          _typingTimer = Timer(const Duration(seconds: 10), () {
            if (mounted) {
              setState(() {
                _isTyping = false;
              });
            }
          });
        } else {
          _typingTimer?.cancel();
        }
      });
    }
  }

  // 启动检查过期消息的计时器
  void _startMessageExpirationTimer() {
    _messageExpirationTimer?.cancel();
    _messageExpirationTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (mounted) {
        _chatNotifier.checkAndDeleteExpiredMessages();
      }
    });
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final route = ModalRoute.of(context);
      if (route is PageRoute) {
        AppRouter.routeObserver.subscribe(this, route);
      }

      _fetchOtherUser();

      _conversationNotifier = ref.read(conversationProvider.notifier);
      _chatNotifier = ref.read(chatProvider(widget.conversationId).notifier);
      _chatSocketNotifier = ref.read(chatSocketProvider.notifier);

      // 预处理阶段：先处理过期消息，避免UI显示后的抖动
      await _chatNotifier.checkAndDeleteExpiredMessages();

      // 标记消息为已读
      await _setAsRead();

      // 启动定期检查过期消息的计时器
      _startMessageExpirationTimer();
    });
  }

  @override
  void didPush() {
    LogUtils.d('didPush', tag: 'ChatPage');
  }

  @override
  void didPop() {
    LogUtils.d('didPop', tag: 'ChatPage');
    _setAsRead();
  }

  @override
  void didPushNext() {
    LogUtils.d('didPushNext', tag: 'ChatPage');
  }

  @override
  void didPopNext() {
    LogUtils.d('didPopNext', tag: 'ChatPage');
    _setAsRead();
  }

  @override
  void dispose() {
    _typingTimer?.cancel();
    _messageExpirationTimer?.cancel();
    AppRouter.routeObserver.unsubscribe(this);
    super.dispose();
  }

  void _fetchOtherUser() async {
    final result = await ref
        .read(profileRepositoryProvider.notifier)
        .getUserInfoById(widget.conversationId);
    result.fold(
      (error) => LogUtils.e(error.message, tag: 'ChatPage._fetchOtherUser'),
      (data) {
        if (mounted) {
          setState(() => _otherUser = data);
        }
      },
    );
  }

  Future<void> _setAsRead() async {
    final conversationResult = await _conversationNotifier
        .getOrCreateConversation(widget.conversationId);
    await conversationResult.fold((left) async => Left(left), (right) async {
      final unReadMessagesResult =
          await _chatNotifier.getReceiveUnreadMessages();

      var unReadMessages = unReadMessagesResult.getRight() ?? [];

      if (unReadMessages.isEmpty) {
        await _conversationNotifier.updateConversation(
          right.copyWith(
            unreadCount: 0,
          ),
        );
        return;
      }

      LogUtils.d('unReadMessages: ${unReadMessages.length}', tag: 'ChatPage');

      for (final message in unReadMessages) {
        await _chatNotifier.setMessageAsRead(message.id);
      }

      final lastMessage = unReadMessages.last;

      await _conversationNotifier.updateConversation(
        right.copyWith(
          unreadCount: 0,
          lastReadMessage: lastMessage.copyWith(status: types.Status.seen),
        ),
      );

      if (lastMessage.status != types.Status.seen) {
        await _chatSocketNotifier.emitMessageRead(
          messageId: lastMessage.remoteId ?? lastMessage.id,
          clearCount: true,
        );
      }
    });
  }

  // 创建基本操作的action
  List<ActionSheetAction> _buildBasicActions(BuildContext context) {
    return [
      ActionSheetAction(
        onPressed: () async {
          final result = await context.showOkCancelAlertDialog(
            title: 'Clear Chat',
            content:
                'Are you sure you want to clear this chat? This action cannot be undone.',
            dialogTag: 'clear_chat',
            confirmText: 'Clear',
            cancelText: 'Cancel',
          );
          if (result == true) {
            await ref
                .read(chatProvider(widget.conversationId).notifier)
                .clearMessages();
            await ref
                .read(conversationProvider.notifier)
                .updateLastMessage(widget.conversationId, null);
          }
        },
        title: 'Clear Chat',
      ),
      ActionSheetAction(
        onPressed: () async {
          await context.push(
            WidgetPageConfig(
              page: EphemeralSetting(
                conversationId: widget.conversationId,
              ),
            ),
          );
        },
        title: 'Ephemeral Messages',
      ),
      ActionSheetAction(
        onPressed: () async {
          await toReportUserPage(
            IMReportSource(conversationId: widget.conversationId),
          );
        },
        title: 'Report User',
      ),
    ];
  }

  // 创建用户关系相关的action
  List<ActionSheetAction> _buildUserRelationActions(BuildContext context) {
    if (_otherUser == null) return [];

    return [
      ActionSheetAction(
        onPressed: () async {
          VoidResult result;
          if (_otherUser!.isFollowing) {
            result = await ref
                .read(followProvider.notifier)
                .unFollowUser(widget.conversationId);
          } else {
            result = await ref.read(followProvider.notifier).followUser(
                  widget.conversationId,
                  origin: FollowOrigin.instantChat,
                );
          }

          result.fold(
            (error) => LoadingUtils.showToast(error.message),
            (_) {
              if (mounted) {
                setState(() {
                  _otherUser = _otherUser!
                      .copyWith(isFollowing: !_otherUser!.isFollowing);
                });
              }
              LoadingUtils.showToast(
                _otherUser!.isFollowing
                    ? 'Followed Successfully'
                    : 'Unfollowed Successfully',
              );
            },
          );
        },
        title: _otherUser!.isFollowing
            ? context.l10n.unfollow
            : context.l10n.follow,
      ),
    ];
  }

  // 创建危险操作的action
  List<ActionSheetAction> _buildDangerousActions(BuildContext context) {
    final actions = <ActionSheetAction>[
      ActionSheetAction(
        onPressed: () async {
          final dialogContext = context;
          // 使用confirmDialog确认删除操作
          final confirmed = await context.showOkCancelAlertDialog(
            title: 'Delete Conversation',
            content:
                'Are you sure you want to delete this conversation? This action cannot be undone.',
            dialogTag: 'delete_conversation',
            confirmText: 'Delete',
            cancelText: 'Cancel',
          );

          if (confirmed == true) {
            await LoadingUtils.showLoading();

            // 删除会话
            await ref.read(conversationProvider.notifier).deleteConversations(
              [widget.conversationId],
              ack: (_) {},
            );

            if (!dialogContext.mounted) return;

            LoadingUtils.dismiss();
            dialogContext.pop();
          }
        },
        isDestructiveAction: true,
        title: 'Delete Conversation',
      ),
    ];

    // 添加屏蔽用户选项，如果有用户信息
    if (_otherUser != null) {
      actions.add(
        ActionSheetAction(
          onPressed: () async {
            final confirmed = await context.showOkCancelAlertDialog(
              title: 'Block User',
              content: 'Are you sure you want to block this user?',
              dialogTag: 'block_user',
              confirmText: 'Block',
              cancelText: 'Cancel',
            );

            if (confirmed == true) {
              // 使用 BlockMixin 中的方法
              block(widget.conversationId);
            }
          },
          isDestructiveAction: true,
          title: 'Block User',
        ),
      );
    }

    return actions;
  }

  Future<void> _showMoreOptions() async {
    // 合并所有actions，将危险操作放在最后
    final actions = [
      ..._buildBasicActions(context),
      ..._buildUserRelationActions(context),
      ..._buildDangerousActions(context),
    ];

    context.showPlatformActionSheet(
      actions: actions,
      dialogTag: 'chat_options',
      cancelButton: CupertinoActionSheetAction(
        onPressed: () => context.pop(),
        child: const Text('Cancel'),
      ),
    );
  }

  void _onSendMessage(MessageConfig config) async {
    final result = await ref
        .read(chatProvider(widget.conversationId).notifier)
        .sendMessage(config);
    result.fold((left) {
      LoadingUtils.showToast(left.message);
    }, (right) {
      // 处理成功
    });
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      contentPadding: EdgeInsets.zero,
      presetSafeArea: false,
      appBar: AppBar(
        backgroundColor: const Color(0xff111111),
        elevation: 0,
        leading: IconButton(
          onPressed: () {
            context.pop();
          },
          icon: Icon(
            CupertinoIcons.back,
            color: context.colorScheme.primary,
          ),
        ),
        title: Row(
          children: [
            AvatarWithFrame(
              width: 35.w,
              height: 35.w,
              avatarUrl: widget.peer.imageUrl ?? '',
            ),
            8.horizontalSpace,
            Container(
              width: 8.w,
              height: 8.w,
              decoration: BoxDecoration(
                color: widget.userOnlineStatus?.isOnline == true
                    ? context.colorScheme.primary
                    : const Color(0xff939393),
                shape: BoxShape.circle,
              ),
            ),
            8.horizontalSpace,
            Flexible(
              child: Text(
                _isTyping
                    ? 'Typing...'
                    : widget.peer.firstName ?? 'Unknown name',
                style: context.textTheme.bodyMediumBold,
              ),
            )
          ],
        ),
        actions: [
          LiveTagBuilder(
            userRoomStatus: widget.userRoomStatus,
            showDot: true,
            builder: (context, onTap, liveTag) {
              return GestureDetector(
                onTap: () => onTap(),
                child: SizedBox(
                  width: 52.w,
                  height: 20.h,
                  child: liveTag,
                ),
              );
            },
          ),
          16.horizontalSpace,
          AppButton(
            padding: EdgeInsets.zero,
            type: AppButtonType.contained,
            onPressed: _showMoreOptions,
            child: const Icon(Icons.more_horiz),
          ),
          16.horizontalSpace,
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0, 0.7, 0.9],
            colors: [
              Color(0xff111111),
              Color(0xff292929),
              Color(0xff373737),
            ],
          ),
        ),
        child: Builder(builder: (context) {
          final chatState = ref.watch(chatProvider(widget.conversationId));

          return chatState.when(data: (data) {
            return _buildChat(data);
          }, error: (error, stack) {
            return Center(
              child: Text('Error: $error'),
            );
          }, loading: () {
            return const Center(
              child: CircularProgressIndicator(),
            );
          });
        }),
      ),
    );
  }

  Widget _buildChat(List<types.Message> messages) {
    return CustomChat(
      conversationId: widget.conversationId,
      messages: messages,
      user: currentUser,
      peer: widget.peer,
      typingUsers: const [],
      onSendText: (text) {
        _onSendMessage(
          TextMessageConfig(content: text, userId: currentUser.id),
        );
      },
    );
  }
}
