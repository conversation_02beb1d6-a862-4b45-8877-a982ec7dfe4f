import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/utils/app_lifecycle.dart'
    as app_lifecycle;
import 'package:flutter_audio_room/core/widgets/refresh_list_view.dart';
import 'package:flutter_audio_room/features/chat/data/model/user_online_status_model.dart';
import 'package:flutter_audio_room/features/chat/data/model/user_room_status_model.dart';
import 'package:flutter_audio_room/features/chat/presentation/controllers/chat_socket_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/pages/mixin/conversation_handle_mixin.dart';
import 'package:flutter_audio_room/features/chat/presentation/pages/mixin/get_offline_conversations_mixin.dart';
import 'package:flutter_audio_room/features/chat/presentation/pages/mixin/get_offline_msg_mixin.dart';
import 'package:flutter_audio_room/features/chat/presentation/pages/mixin/message_read_handle_mixin.dart';
import 'package:flutter_audio_room/features/chat/presentation/pages/mixin/user_status_listener_mixin.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/user_status_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/conversation_list_item.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/models/conversation_model.dart';
import '../providers/conversation_provider.dart';
import 'chat_page.dart';

class ConversationListPage extends ConsumerStatefulWidget {
  const ConversationListPage({super.key});

  @override
  ConsumerState<ConversationListPage> createState() =>
      _ConversationListPageState();
}

class _ConversationListPageState extends ConsumerState<ConversationListPage>
    with
        ConversationHandleMixin,
        GetOfflineConversationsMixin,
        GetOfflineMsgMixin,
        MessageReadHandleMixin,
        UserStatusListenerMixin {
  // 用于标识生命周期回调的键
  static const String _lifecycleCallbackKey = 'conversation_list_page';
  // 后端单次查询用户状态的最大数量限制
  static const int _maxUserQueryBatchSize = 20;

  @override
  void initState() {
    super.initState();

    // 注册应用从后台恢复时的回调
    app_lifecycle.registerResumeCallback(
      _lifecycleCallbackKey,
      () async {
        await triggerSyncOfflineBasic();
        // 从后台恢复时查询当前已加载的会话中用户的在线状态
        final conversations = await ref.read(conversationProvider.future);
        if (conversations.isNotEmpty) {
          await _queryUserOnlineStatus(conversations);
        }
      },
    );

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final chatSocketNotifier = ref.read(chatSocketProvider.notifier);

      await chatSocketNotifier.initializeSocket();
      onLoginExpiredEvent();
      _checkConnectInfo(chatSocketNotifier);
      onReadMessage();
      onReceiveMessageEvent();
    });
  }

  @override
  void dispose() {
    // 在组件销毁时移除回调
    app_lifecycle.unregisterResumeCallback(_lifecycleCallbackKey);
    super.dispose();
  }

  // 实现 UserStatusListenerMixin 的回调方法
  @override
  void onUserOnlineStatusChanged(UserOnlineStatusModel userStatus) {
    if (userStatus.userId != null) {
      ref
          .read(userOnlineStatusCacheProvider.notifier)
          .updateUserStatus(userStatus.userId!, userStatus);
    }
  }

  @override
  void onUserRoomStatusChanged(UserRoomStatusModel roomStatus) {
    if (roomStatus.userId != null) {
      ref
          .read(userRoomStatusCacheProvider.notifier)
          .updateUserRoomStatus(roomStatus.userId!, roomStatus);
    }
  }

  @override
  Future<void> onConversationsSynced(List<dynamic> list) async {
    for (final conversation in list) {
      if (conversation.conversationId == null) continue;
      await triggerSyncOfflineMsg(conversation.conversationId!);
    }
  }

  // 查询用户在线状态和房间信息，支持分批处理
  Future<void> _queryUserOnlineStatus(
      List<ConversationModel> conversations) async {
    try {
      if (conversations.isEmpty) {
        return;
      }

      final userRoomStatusCacheNotifier =
          ref.read(userRoomStatusCacheProvider.notifier);
      final userOnlineStatusCacheNotifier =
          ref.read(userOnlineStatusCacheProvider.notifier);

      // 提取所有会话中的用户ID
      final userIds = conversations
          .map((conversation) => conversation.peer.id)
          .where((id) => id.isNotEmpty)
          .toSet()
          .toList();

      if (userIds.isEmpty) {
        return;
      }

      // 将用户ID分批处理，每批最多 _maxUserQueryBatchSize 个
      for (int i = 0; i < userIds.length; i += _maxUserQueryBatchSize) {
        final int endIndex = (i + _maxUserQueryBatchSize < userIds.length)
            ? i + _maxUserQueryBatchSize
            : userIds.length;
        final batchUserIds = userIds.sublist(i, endIndex);
        
        // 查询在线状态
        final userOnlineStatusMap = await queryUserOnlineStatus(batchUserIds);
        if (userOnlineStatusMap != null) {
          userOnlineStatusCacheNotifier.updateMultipleUserStatuses(
            userOnlineStatusMap,
          );
        }
        
        // 查询房间信息
        final userRoomInfos = await queryUserRoomInfos(batchUserIds);
        userRoomStatusCacheNotifier.updateMultipleUserRoomStatuses(
          userRoomInfos,
        );
      }
    } catch (e) {
      // 错误处理，不影响会话列表的正常显示
      debugPrint('Error querying user status: $e');
    }
  }

  Future<void> _checkConnectInfo(ChatSocket chatSocketNotifier) async {
    await chatSocketNotifier.emitWithAck(
      ChatSocketEvents.connectInfo,
      {},
      (data) async {
        if (data['isNewDevice'] == true) {
          await ref
              .read(conversationProvider.notifier)
              .initSignal(forceUpdate: true);
          final conversations = await ref.read(conversationProvider.future);
          for (final conversation in conversations) {
            await ref
                .read(conversationProvider.notifier)
                .setNeedRecreateSession(
                  conversation.id,
                  needRecreateSession: true,
                );
          }
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final conversationsAsync = ref.watch(conversationProvider);

    return Scaffold(
      body: conversationsAsync.when(
        data: (conversations) => _ConversationList(
          conversations: conversations,
          onRefresh: () async {
            await triggerSyncOfflineBasic();
            return await ref.read(conversationProvider.notifier).refresh();
          },
          queryUserStatus: _queryUserOnlineStatus,
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(child: Text('error: $error')),
      ),
    );
  }
}

class _ConversationList extends ConsumerStatefulWidget {
  const _ConversationList({
    required this.conversations,
    required this.onRefresh,
    required this.queryUserStatus,
  });

  final List<ConversationModel> conversations;
  final Future<List<ConversationModel>> Function() onRefresh;
  final Future<void> Function(List<ConversationModel>) queryUserStatus;

  @override
  ConsumerState<_ConversationList> createState() => _ConversationListState();
}

class _ConversationListState extends ConsumerState<_ConversationList> {
  List<ConversationModel> _dataList = [];
  bool _isLoading = false;
  dynamic _error;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    _dataList = widget.conversations;
    // 初始加载后查询用户状态
    if (widget.conversations.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.queryUserStatus(widget.conversations);
      });
    }
  }

  @override
  void didUpdateWidget(_ConversationList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.conversations != oldWidget.conversations) {
      _dataList = widget.conversations;
    }
  }

  Future<void> _onRefresh() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final refreshedConversations = await widget.onRefresh();

      setState(() {
        _dataList = refreshedConversations;
        _hasMore = true;
      });

      // 刷新后查询用户状态
      if (refreshedConversations.isNotEmpty) {
        await widget.queryUserStatus(refreshedConversations);
      }
    } catch (error) {
      setState(() {
        _error = error;
      });
      rethrow;
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _onLoadMore() async {
    if (!_hasMore || _isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final newConversations =
          await ref.read(conversationProvider.notifier).loadMore(_dataList);

      // 获取新加载的会话
      if (newConversations.length > _dataList.length) {
        final newlyLoadedConversations =
            newConversations.sublist(_dataList.length);

        setState(() {
          _dataList = newConversations;
          _hasMore = newlyLoadedConversations.isNotEmpty;
        });

        // 为新加载的会话查询用户状态
        if (newlyLoadedConversations.isNotEmpty) {
          await widget.queryUserStatus(newlyLoadedConversations);
        }
      } else {
        setState(() {
          _hasMore = false;
        });
      }
    } catch (error) {
      setState(() {
        _error = error;
      });
      rethrow;
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return RefreshListView<ConversationModel>(
      dataList: _dataList,
      isLoading: _isLoading,
      error: _error,
      hasMore: _hasMore,
      onRefresh: _onRefresh,
      onLoadMore: _onLoadMore,
      itemBuilder: (context, conversation, index) {
        final userId = conversation.peer.id;
        final userOnlineStatusCache = ref.watch(userOnlineStatusCacheProvider);
        final userRoomStatusCache = ref.watch(userRoomStatusCacheProvider);
        final userOnlineStatus = userOnlineStatusCache[userId];
        final userRoomStatus = userRoomStatusCache[userId];

        return ConversationListItem(
          conversation: conversation,
          userOnlineStatus: userOnlineStatus,
          userRoomStatus: userRoomStatus,
          onTap: () => context.push(
            RoutePageConfig(
              route: ChatPage.route(
                conversation.id,
                conversation.peer,
                userRoomStatus,
                userOnlineStatus,
              ),
            ),
          ),
        );
      },
      separatorBuilder: (context, index) => const Divider(),
      emptyBuilder: (context) => const Center(
        child: Text('No conversations yet'),
      ),
    );
  }
}
