import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/widgets/avatar_with_frame.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/chat_provider.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_context_menu/flutter_context_menu.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:super_context_menu/super_context_menu.dart';

/// 消息气泡组件，用于自定义消息气泡样式
class ChatBubble extends ConsumerStatefulWidget {
  final Widget child;
  final String conversationId;
  final types.Message message;
  final types.User peer;
  final Menu menu;
  final bool nextMessageInGroup;
  final bool isSelfMessage;
  final types.User currentUser;

  const ChatBubble({
    super.key,
    required this.child,
    required this.conversationId,
    required this.message,
    required this.peer,
    required this.menu,
    required this.nextMessageInGroup,
    required this.isSelfMessage,
    required this.currentUser,
  });
  
  @override
  ConsumerState<ChatBubble> createState() => _ChatBubbleState();
}

class _ChatBubbleState extends ConsumerState<ChatBubble> {
  // 添加 GlobalKey 用于获取 Row 的位置
  final GlobalKey _key = GlobalKey();

  void _onMessageStatusTap(BuildContext context, types.Message message) {
    if (message.status == types.Status.error &&
        message.type != types.MessageType.system) {
      showCupertinoModalPopup(
        context: context,
        builder: (context) {
          return CupertinoActionSheet(
            title: const Text('Failed to send message'),
            actions: [
              CupertinoActionSheetAction(
                onPressed: () async {
                  context.pop();
                  final result = await ref
                      .read(chatProvider(widget.conversationId).notifier)
                      .retryMessage(message);
                  result.fold(
                    (error) => LoadingUtils.showError(error.message),
                    (data) => null,
                  );
                },
                child: const Text('Retry'),
              ),
            ],
            cancelButton: CupertinoActionSheetAction(
              onPressed: () => context.pop(),
              child: const Text('Cancel'),
            ),
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentUserIsAuthor =
        widget.message.author.id == widget.currentUser.id;
    final messageBorderRadius = 20.r;
    final borderRadius = BorderRadius.circular(messageBorderRadius);

    final bgColor = (currentUserIsAuthor
        ? context.theme.colorScheme.primary
        : const Color(0xff6C6C6C));

    final entries = <ContextMenuEntry>[];
    for (final action in widget.menu.children.cast<MenuAction>()) {
      entries.add(
        MenuItem(
          label: action.title ?? '',
          onSelected: action.callback,
        ),
      );
    }

    // 计算消息气泡的最大宽度（屏幕宽度的75%）
    final screenWidth = MediaQuery.of(context).size.width;
    final maxBubbleWidth = screenWidth * 0.75;

    return Row(
      mainAxisAlignment:
          currentUserIsAuthor ? MainAxisAlignment.end : MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (!widget.isSelfMessage) ...[
          AvatarWithFrame(
            avatarUrl: widget.peer.imageUrl ?? '',
            width: 35.w,
            height: 35.w,
          ),
          10.horizontalSpace,
        ] else ...[
          _buildMessageStatus(widget.message),
          10.horizontalSpace,
        ],
        ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: maxBubbleWidth,
          ),
          child: IntrinsicWidth(
            key: _key,
            child: GestureDetector(
              onTap: widget.message.status == types.Status.error
                  ? () => _onMessageStatusTap(context, widget.message)
                  : null,
              onLongPress: () {
                final RenderBox? renderBox =
                    _key.currentContext?.findRenderObject() as RenderBox?;
                if (renderBox != null) {
                  final position = renderBox.localToGlobal(Offset.zero);
                  final size = renderBox.size;        
                  final screenHeight = MediaQuery.of(context).size.height;
                  final isNearBottom =
                      position.dy + size.height + 150 > screenHeight;
                  final menuHeight = 32 * entries.length;
                  final menuPosition = Offset(
                    currentUserIsAuthor
                        ? position.dx + size.width
                        : position.dx,
                    isNearBottom
                        ? position.dy - menuHeight - 10.h
                        : position.dy + size.height + 10.h,
                  );
        
                  final menu = ContextMenu(
                    entries: entries,
                    position: menuPosition,
                    padding: EdgeInsets.zero,
                    borderRadius: BorderRadius.circular(16.r),
                  );
                  menu.show(context);
                } else {
                  final menu = ContextMenu(
                    entries: entries,
                    padding: EdgeInsets.zero,
                    borderRadius: BorderRadius.circular(16.r),
                  );
                  menu.show(context);
                }
              },
              child: _buildContextMenu(
                context,
                bgColor: bgColor,
                borderRadius: borderRadius,
                child: widget.child,
                currentUserIsAuthor: currentUserIsAuthor,
              ),
            ),
          ),
        ),
        if (widget.isSelfMessage) ...[
          10.horizontalSpace,
          AvatarWithFrame(
            avatarUrl: widget.currentUser.imageUrl ?? '',
            width: 35.w,
            height: 35.w,
          ),
        ],
      ],
    );
  }

  Widget _buildMessageStatus(types.Message message) {
    // 只为自己发送的消息显示状态，且不为系统消息
    if (widget.isSelfMessage && message.type == types.MessageType.system) {
      return const SizedBox.shrink();
    }

    // 构建不同状态的指示器
    Widget statusWidget;
    switch (message.status) {
      case types.Status.sending:
        statusWidget = SizedBox(
          width: 10.w,
          height: 10.w,
          child: CircularProgressIndicator(
            strokeWidth: 1.5,
            valueColor: AlwaysStoppedAnimation<Color>(
              context.theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
        );
        break;
      case types.Status.error:
        statusWidget = Icon(
          Icons.refresh,
          size: 15.w,
          color: Colors.red,
        );
        break;
      default:
        return const SizedBox.shrink();
    }

    // 包装状态指示器
    return Padding(
      padding: EdgeInsets.only(bottom: 10.h),
      child: GestureDetector(
        onTap: message.status == types.Status.error
            ? () => _onMessageStatusTap(context, message)
            : null,
        child: statusWidget,
      ),
    );
  }

  Widget _buildContextMenu(
    BuildContext context, {
    required Color bgColor,
    required BorderRadius borderRadius,
    required Widget child,
    required bool currentUserIsAuthor,
  }) {
    Widget childWidget;

    // 文本消息：使用背景色
    childWidget = Container(
      decoration: BoxDecoration(
        color: bgColor,
        border: Border.all(
          color: bgColor,
          width: 1.0,
        ),
        borderRadius: borderRadius,
      ),
      child: Builder(builder: (context) {
        if ([types.MessageType.image, types.MessageType.video]
            .contains(widget.message.type)) {
          return ClipRRect(
            borderRadius: borderRadius,
            child: child,
          );
        }
        return Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
              fit: FlexFit.loose,
              child: child,
            ),
            SizedBox(width: 8.w),
          ],
        );
      }
        ),
    );

    if (Platform.isIOS) {
      return ContextMenuWidget(
        menuProvider: (request) => widget.menu,
        hitTestBehavior: HitTestBehavior.opaque,
        mobileMenuWidgetBuilder: DefaultMobileMenuWidgetBuilder(
          enableBackgroundBlur: false,
        ),
        child: childWidget,
      );
    }

    return childWidget;
  }
}
