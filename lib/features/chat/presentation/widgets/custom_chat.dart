import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_audio_room/core/configs/refresh_config.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/list_ext.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/chat/domain/models/message_subtype.dart';
import 'package:flutter_audio_room/features/chat/presentation/controllers/chat_input_controller.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/chat_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/chat/chat_bottom_widget.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/chat/chat_bubble.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/message/invite_room_message_widget.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/message/text_message_widget.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/message/voice_call_message_widget.dart';
import 'package:flutter_audio_room/services/core_service/core_service_provider.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:super_context_menu/super_context_menu.dart';

class CustomChat extends ConsumerStatefulWidget {
  const CustomChat({
    super.key,
    required this.conversationId,
    required this.messages,
    required this.user,
    required this.typingUsers,
    required this.onSendText,
    required this.peer,
  });

  final String conversationId;
  final List<types.Message> messages;
  final types.User user;
  final types.User peer;
  final List<types.User> typingUsers;
  final void Function(String) onSendText;

  @override
  ConsumerState<CustomChat> createState() => _CustomChatState();
}

class _CustomChatState extends ConsumerState<CustomChat>
    with SingleTickerProviderStateMixin {
  final RefreshController _refreshController = RefreshController();

  bool _hasMore = true;

  // 分组时间间隔（毫秒）
  static const int _timeGroupInterval = 5 * 60 * 1000; // 5分钟

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final inputController =
        ref.watch(chatInputControllerProvider(widget.conversationId));
    final maxTextLength =
        ref.watch(coreServiceProvider).config.chatConfigResp.maxContentLength;

    return KeyboardDismissOnTap(
      child: Column(
        children: [
          // 聊天消息列表
          Expanded(
            child: _buildChatList(),
          ),

          // 打字指示器
          if (widget.typingUsers.isNotEmpty) _buildTypingIndicator(),

          // 底部输入区域
          ChatBottomWidget(
            conversationId: widget.conversationId,
            onSendText: () => _handleSendText(inputController),
            maxTextLength: maxTextLength,
          ),
        ],
      ),
    );
  }

  /// 构建聊天列表
  Widget _buildChatList() {
    // 准备消息列表，并按时间排序（从新到旧）
    final sortedMessages = widget.messages.toList();

    if (sortedMessages.isEmpty) {
      return SmartRefresher(
        controller: _refreshController,
        reverse: true,
        enablePullDown: false,
        enablePullUp: _hasMore,
        onLoading: _onLoading,
        footer: RefreshConfig.defaultFooter(),
        child: const Center(child: Text('No messages')),
      );
    }

    // 生成包含日期标头和消息的列表项
    final listItems = <_ChatListItem>[];
    int? lastTimeGroup;

    for (int i = 0; i < sortedMessages.reversed.length; i++) {
      final message = sortedMessages.reversed.toList()[i];
      final messageTime = message.createdAt ?? 0;

      // 计算时间组（向下取整到5分钟间隔）
      final timeGroup =
          (messageTime ~/ _timeGroupInterval) * _timeGroupInterval;

      // 如果是新的时间组，添加时间标头
      if (lastTimeGroup == null || lastTimeGroup != timeGroup) {
        listItems.add(_ChatListItem(
          type: _ChatListItemType.dateHeader,
          message: message,
          timestamp: messageTime,
        ));
        lastTimeGroup = timeGroup;
      }

      // 检查是否与下一条消息是同一组
      final isNextMessageInGroup = i < sortedMessages.length - 1 &&
          sortedMessages[i + 1].author.id == message.author.id &&
          _isWithinTimeWindow(message, sortedMessages[i + 1]);

      // 添加消息
      listItems.add(_ChatListItem(
        type: _ChatListItemType.message,
        message: message,
        isNextInGroup: isNextMessageInGroup,
      ));
    }

    return SmartRefresher(
      controller: _refreshController,
      reverse: true,
      enablePullDown: false,
      enablePullUp: _hasMore,
      onLoading: _onLoading,
      header: const WaterDropHeader(),
      footer: CustomFooter(
        builder: (BuildContext context, LoadStatus? mode) {
          Widget body;
          if (mode == LoadStatus.idle) {
            body = const Text("Pull up to load more");
          } else if (mode == LoadStatus.loading) {
            body = const CupertinoActivityIndicator();
          } else if (mode == LoadStatus.failed) {
            body = const Text("Load failed, click to retry");
          } else if (mode == LoadStatus.canLoading) {
            body = const Text("Release to load more");
          } else {
            body = const Text("No more data");
          }
          return SizedBox(
            height: 55.0,
            child: Center(child: body),
          );
        },
      ),
      child: ListView.builder(
        keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
        reverse: true,
        padding: EdgeInsets.symmetric(vertical: 16.h),
        itemCount: listItems.length,
        itemBuilder: (context, index) {
          final item = listItems.reversed.toList()[index]; // 正常顺序访问

          if (item.type == _ChatListItemType.dateHeader) {
            return _buildDateHeader(item.message);
          } else {
            return Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 4.h,
              ),
              child: _buildMessageWidget(
                item.message,
                item.isNextInGroup,
                _isSelfMessage(item.message),
              ),
            );
          }
        },
      ),
    );
  }

  /// 上拉加载更多回调
  Future<void> _onLoading() async {
    try {
      final hasMore = await ref
          .read(chatProvider(widget.conversationId).notifier)
          .loadMore();
      setState(() {
        _hasMore = hasMore;
      });
      _refreshController.loadComplete();
    } catch (e) {
      _refreshController.loadFailed();
      LogUtils.e('Error loading more messages: $e', tag: 'CustomChat');
    }
  }

  /// 判断是否为自己发送的消息
  bool _isSelfMessage(types.Message message) {
    return message.author.id == widget.user.id;
  }

  /// 判断两条消息是否在指定时间窗口内（用于分组）
  bool _isWithinTimeWindow(types.Message message1, types.Message message2) {
    if (message1.createdAt == null || message2.createdAt == null) return false;
    return (message1.createdAt! - message2.createdAt!).abs() <
        _timeGroupInterval;
  }

  /// 获取消息日期
  DateTime _getMessageDate(types.Message message) {
    return message.createdAt != null
        ? DateTime.fromMillisecondsSinceEpoch(message.createdAt!)
        : DateTime.now();
  }

  /// 判断两个日期是否是同一天
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// 构建日期标头
  Widget _buildDateHeader(types.Message message) {
    final messageDate = _getMessageDate(message);
    final now = DateTime.now();
    final yesterday = now.subtract(const Duration(days: 1));

    // 显示详细的时间格式
    String dateText;
    String timeText = DateFormat('HH:mm').format(messageDate);

    if (_isSameDay(messageDate, now)) {
      dateText = timeText;
    } else if (_isSameDay(messageDate, yesterday)) {
      dateText = 'Yesterday $timeText';
    } else {
      // 不同年份显示完整日期，否则只显示月日
      if (messageDate.year != now.year) {
        dateText = DateFormat('yyyy/MM/dd HH:mm').format(messageDate);
      } else {
        dateText = DateFormat('MM/dd HH:mm').format(messageDate);
      }
    }

    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.h),
      child: Center(
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: context.theme.colorScheme.surface.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: context.theme.colorScheme.onSurface.withValues(alpha: 0.1),
            ),
          ),
          child: Text(
            dateText,
            style: TextStyle(
              color: context.theme.colorScheme.onSurface.withValues(alpha: 0.6),
              fontSize: 11.sp,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建单个消息组件
  Widget _buildMessageWidget(
      types.Message message, bool isNextMessageInGroup, bool isSelfMessage) {
    // 系统消息单独处理
    if (message.type == types.MessageType.system) {
      return _buildSystemMessage(message as types.SystemMessage);
    }

    Widget messageContent;

    // 根据消息类型构建不同的内容
    switch (message.type) {
      case types.MessageType.text:
        messageContent = _buildTextMessage(message as types.TextMessage);
        break;
      default:
        messageContent = const SizedBox.shrink();
    }

    if (message.type == types.MessageType.custom) {
      final subtype = MessageSubtype.values.firstWhereOrNull((e) {
        return e.type == int.tryParse(message.metadata?['subtype'] ?? '');
      });
      switch (subtype) {
        case MessageSubtype.VOICE_CALL:
          messageContent =
              _buildVoiceCallMessage(message as types.CustomMessage);
        case MessageSubtype.INVITE_ROOM:
          return _buildInviteRoomMessage(message as types.CustomMessage);
        default:
          return const SizedBox.shrink();
      }
    }

    return ChatBubble(
      conversationId: widget.conversationId,
      peer: widget.peer,
      isSelfMessage: isSelfMessage,
      message: message,
      menu: _buildContextMenu(message),
      nextMessageInGroup: isNextMessageInGroup,
      currentUser: widget.user,
      child: messageContent,
    );
  }
  
  /// 构建系统消息
  Widget _buildSystemMessage(types.SystemMessage message) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.h, horizontal: 40.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: context.theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: context.theme.colorScheme.onSurface.withValues(alpha: 0.1),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.info_outline,
            size: 14.sp,
            color: context.theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          SizedBox(width: 8.w),
          Flexible(
            child: Text(
              message.text,
              style: TextStyle(
                color:
                    context.theme.colorScheme.onSurface.withValues(alpha: 0.6),
                fontSize: 12.sp,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建文本消息
  Widget _buildTextMessage(types.TextMessage message) {
    final isFromMe = message.author.id == widget.user.id;

    return TextMessageWidget(
      message: message,
      isFromMe: isFromMe,
    );
  }

  Widget _buildVoiceCallMessage(types.CustomMessage message) {
    final isFromMe = message.author.id == widget.user.id;
    return VoiceCallMessageWidget(
      message: message,
      conversationId: widget.conversationId,
      isFromMe: isFromMe,
    );
  }

  Widget _buildInviteRoomMessage(types.CustomMessage message) {
    return InviteRoomMessageWidget(
      message: message,
      conversationId: widget.conversationId,
    );
  }

  /// 构建打字指示器
  Widget _buildTypingIndicator() {
    if (widget.typingUsers.isEmpty) {
      return const SizedBox.shrink();
    }

    final names = widget.typingUsers.map((user) => user.firstName).join(', ');

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      alignment: Alignment.centerLeft,
      color: context.theme.colorScheme.surface,
      child: Row(
        children: [
          _buildTypingAnimation(),
          8.horizontalSpace,
          Expanded(
            child: Text(
              '$names 正在输入...',
              style: TextStyle(
                fontSize: 14.sp,
                color:
                    context.theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建打字动画
  Widget _buildTypingAnimation() {
    return SizedBox(
      width: 40.w,
      height: 20.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(
          3,
          (index) => _buildTypingDot(index),
        ),
      ),
    );
  }

  /// 构建打字动画的点
  Widget _buildTypingDot(int index) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0, end: 1),
      duration: Duration(milliseconds: 300 + index * 200),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        return Opacity(
          opacity: (0.4 + 0.6 * (value > 0.5 ? 1 - value : value) * 2),
          child: Container(
            width: 6.w,
            height: 6.w,
            decoration: BoxDecoration(
              color: context.theme.colorScheme.primary,
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }

  /// 处理发送聊天信息
  void _handleSendText(ChatInputController controller) {
    if (controller.getText().isNotEmpty) {
      HapticFeedback.mediumImpact();
      controller.sendText(widget.onSendText);
    }
  }

  Menu _buildContextMenu(types.Message message) {
    final List<MenuElement> menuItems = [];

    switch (message.type) {
      case types.MessageType.text:
        final textMessage = message as types.TextMessage;
        menuItems.addAll([
          MenuAction(
            title: 'Copy',
            callback: () {
              Clipboard.setData(ClipboardData(text: textMessage.text));
              LoadingUtils.showToast(
                'Copied to clipboard',
                maskType: EasyLoadingMaskType.clear,
              );
            },
          ),
        ]);
        break;
      case types.MessageType.image:
        break;
      case types.MessageType.video:
        break;
      default:
        break;
    }

    if (message.status == types.Status.error) {
      menuItems.add(
        MenuAction(
          title: 'Resend',
          callback: () async {
            final result = await ref
                .read(chatProvider(widget.conversationId).notifier)
                .retryMessage(message);
            result.fold(
              (error) => LoadingUtils.showError(error.message),
              (data) => null,
            );
          },
        ),
      );
    }

    menuItems.add(
      MenuAction(
        title: 'Delete',
        attributes: const MenuActionAttributes(
          destructive: true,
        ),
        callback: () async {
          final result = await context.showOkCancelAlertDialog(
            title: 'Delete Message',
            content: 'Are you sure you want to delete this message?',
          );
          if (result == true) {
            ref
                .read(chatProvider(widget.conversationId).notifier)
                .deleteMessage(message);
          }
        },
      ),
    );

    return Menu(children: menuItems);
  }
}

/// 聊天列表项类型
enum _ChatListItemType {
  message,
  dateHeader,
}

/// 聊天列表项模型
class _ChatListItem {
  final _ChatListItemType type;
  final types.Message message;
  final bool isNextInGroup;
  final int? timestamp;

  _ChatListItem({
    required this.type,
    required this.message,
    this.isNextInGroup = false,
    this.timestamp,
  });
}
