import 'package:flutter/cupertino.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/join_room_mixin.dart';
import 'package:flutter_audio_room/features/chat/data/model/user_room_status_model.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LiveTagBuilder extends ConsumerStatefulWidget {
  const LiveTagBuilder({
    super.key,
    required this.userRoomStatus,
    this.showDot = false,
    required this.builder,
  });

  final UserRoomStatusModel? userRoomStatus;
  final Function(BuildContext context, Function onTap, Widget liveTag) builder;
  final bool showDot;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _LiveTagBuilderState();
}

class _LiveTagBuilderState extends ConsumerState<LiveTagBuilder>
    with JoinRoomMixin {
  void _onTap(BuildContext context) {
    final roomId = widget.userRoomStatus?.roomId;
    if (roomId == null) {
      return;
    }

    if (widget.userRoomStatus?.status != 1) {
      return;
    }

    navigateToAudioRoom(roomId);
  }

  @override
  Widget build(BuildContext context) {
    return widget.builder(
      context,
      () => _onTap(context),
      _LiveTag(
        key: widget.key,
        userRoomStatus: widget.userRoomStatus,
        showDot: widget.showDot,
      ),
    );
  }
}

class _LiveTag extends StatelessWidget {
  const _LiveTag({
    super.key,
    required this.userRoomStatus,
    this.showDot = false,
  });

  final UserRoomStatusModel? userRoomStatus;
  final bool showDot;

  @override
  Widget build(BuildContext context) {
    if (userRoomStatus?.status != 1) {
      return const SizedBox.shrink();
    }

    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: context.theme.colorScheme.primary,
        borderRadius: BorderRadius.circular(999),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (showDot)
            Container(
              width: 6.w,
              height: 6.w,
              decoration: BoxDecoration(
                color: context.theme.colorScheme.onPrimary,
                shape: BoxShape.circle,
              ),
              margin: EdgeInsets.only(right: 5.w),
            ),
          Text(
            'Live',
            style: TextStyle(
              color: context.theme.colorScheme.onPrimary,
              fontSize: 10.sp,
              height: 0,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
