// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_socket_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$chatSocketHash() => r'4a430a29bbd558631b25f434f94951fb69bfe613';

/// See also [ChatSocket].
@ProviderFor(ChatSocket)
final chatSocketProvider =
    NotifierProvider<ChatSocket, SocketConnectionState>.internal(
  ChatSocket.new,
  name: r'chatSocketProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$chatSocketHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ChatSocket = Notifier<SocketConnectionState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
