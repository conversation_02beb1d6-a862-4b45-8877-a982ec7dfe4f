// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'friend_voice_call_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$friendVoiceCallHash() => r'6f070c004b34644ba76cec88288b97252008c438';

/// See also [FriendVoiceCall].
@ProviderFor(FriendVoiceCall)
final friendVoiceCallProvider =
    AutoDisposeNotifierProvider<FriendVoiceCall, VoiceCallState>.internal(
  FriendVoiceCall.new,
  name: r'friendVoiceCallProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$friendVoiceCallHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FriendVoiceCall = AutoDisposeNotifier<VoiceCallState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
