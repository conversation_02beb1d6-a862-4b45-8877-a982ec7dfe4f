import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/router/app_router.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/localization_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/core/widgets/app_scaffold.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/pages/chat_page.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/conversation_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/user_status_provider.dart';
import 'package:flutter_audio_room/features/instant_chat/domain/entities/match_status.dart';
import 'package:flutter_audio_room/features/instant_chat/presentation/provider/instant_chat_provider.dart';
import 'package:flutter_audio_room/services/core_service/core_service_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class InstantChatMatchScreen extends ConsumerStatefulWidget {
  const InstantChatMatchScreen({super.key});

  @override
  ConsumerState<InstantChatMatchScreen> createState() =>
      _InstantChatMatchScreenState();
}

class _InstantChatMatchScreenState
    extends ConsumerState<InstantChatMatchScreen>
    with RouteAware {
  bool _socketConnected = false;
  Timer? _countdownTimer;
  int _countdown = 3;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      final route = ModalRoute.of(context);
      if (route is PageRoute) {
        AppRouter.routeObserver.subscribe(this, route);
      }
    });

    ref.read(instantChatProvider.notifier).initializeWebSocket().then((_) {
      setState(() {
        _socketConnected = true;
      });
    });

    ref.listenManual(instantChatProvider, (previous, next) {
      if (previous?.matchStatus != next.matchStatus &&
          next.matchStatus is MatchSuccess) {
        _startCountdown();
      }
    });
  }

  @override
  void didPop() {
    ref.read(coreServiceProvider.notifier).getMatchRemainQuota();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    AppRouter.routeObserver.unsubscribe(this);
    super.dispose();
  }

  void _startCountdown() async {
    _countdownTimer?.cancel();
    setState(() {
      _countdown = 3;
    });

    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      setState(() {
        _countdown -= 1;
      });

      if (_countdown <= 0) {
        timer.cancel();
        final state = ref.read(instantChatProvider);
        final peerId = state.peerId;

        if (state.matchStatus is MatchSuccess && peerId != null) {
          final conversation = await ref
              .read(conversationProvider.notifier)
              .getOrCreateConversation(peerId);

          if (conversation.isLeft()) {
            LoadingUtils.showToast(conversation.getLeft()!.message);
            return;
          }

          final userRoomStatus = ref.read(userRoomStatusCacheProvider);
          final userOnlineStatus = ref.read(userOnlineStatusCacheProvider);

          if (!mounted) return;
          context.pushReplacement(
            RoutePageConfig(
              route: ChatPage.route(
                conversation.getRight()!.id,
                conversation.getRight()!.peer,
                userRoomStatus[peerId],
                userOnlineStatus[peerId],
              ),
            ),
          );
        }
      }
    });
  }

  Future<void> _startMatch() async {
    final inAudioRoom = ref
        .read(audioRoomProvider.select((state) => state.currentRoom != null));
    if (inAudioRoom) {
      LoadingUtils.showToast('You are in an audio room');
      return;
    }
    ref.read(instantChatProvider.notifier).startMatch();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        final state = ref.read(instantChatProvider);
        if (state.matchStatus is MatchInProgress) {
          ref.read(instantChatProvider.notifier).cancelMatch();
        }
        if (state.matchStatus is! MatchSuccess) {
          ref.read(instantChatProvider.notifier).dispose();
        }
      },
      child: AppScaffold(
        appBar: AppBar(
          title: Text(context.l10n.instantChat),
        ),
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    final state = ref.watch(instantChatProvider);

    final punishment = state.punishment;

    if (punishment != null) {
      return _restricted(
        punishment.message,
        punishment.identifier,
        punishment.punishment.expireTime,
      );
    }

    switch (state.matchStatus) {
      case MatchNotStarted():
        return _notStarted();
      case MatchInProgress():
        return _inProgress();
      case MatchError(:final message):
        return _error(message);
      case MatchRestricted(:final message, :final code, :final expireTime):
        return _restricted(message, code, expireTime);
      case MatchSuccess():
        return _matchSuccess(state.peerNickName ?? 'Unknown', state.peerAvatar);
    }
  }

  Widget _notStarted() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Instant Chat',
            style: context.textTheme.headlineMedium,
          ),
          20.verticalSpace,
          Text(
            'Start a chat with strangers',
            style: context.textTheme.bodyMedium,
          ),
          40.verticalSpace,
          AppButton(
            onPressed: _startMatch,
            disabled: !_socketConnected,
            isLoading: !_socketConnected,
            text: 'Start Matching',
          ),
        ],
      ),
    );
  }

  Widget _inProgress() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          20.verticalSpace,
          Text(
            'Matching...',
            style: context.textTheme.bodyLarge,
          ),
          10.verticalSpace,
          Text(
            'Please wait, we are looking for a chat partner for you',
            style: context.textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _matchSuccess(String peerName, Map<String, dynamic> peerAvatar) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircleAvatar(
            radius: 60.r,
            backgroundImage: peerAvatar.isNotEmpty
                ? NetworkImage(peerAvatar['url'] as String)
                : null,
            child: peerAvatar.isEmpty ? Icon(Icons.person, size: 60.r) : null,
          ),
          20.verticalSpace,
          Text(
            'Match Success!',
            style: context.textTheme.headlineSmall,
          ),
          10.verticalSpace,
          Text(
            'You have matched with: $peerName',
            style: context.textTheme.bodyLarge,
          ),
          30.verticalSpace,
          Text(
            '$_countdown seconds later, you will be automatically entered into the chat',
            style: context.textTheme.bodyMedium!.copyWith(
              color: context.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _error(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _messageDisplay(message),
          10.verticalSpace,
          AppButton(
            disabled: !_socketConnected,
            onPressed: _startMatch,
            text: 'Retry',
          ),
        ],
      ),
    );
  }

  Widget _restricted(String message, String code, DateTime? expireTime) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _messageDisplay(message),
          10.verticalSpace,
          if (expireTime != null)
            Text(
              'Restriction expiration time: ${DateFormat('yyyy-MM-dd HH:mm').format(expireTime)}',
            ),
        ],
      ),
    );
  }

  Widget _messageDisplay(String message) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Text(
        message,
        textAlign: TextAlign.center,
        style: context.textTheme.bodyMedium,
      ),
    );
  }
}
