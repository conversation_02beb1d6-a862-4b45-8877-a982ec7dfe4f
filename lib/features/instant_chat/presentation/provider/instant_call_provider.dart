import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/core/widgets/audio_player/audio_player_controller.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/utils/callback_handlers.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/instant_chat/domain/entities/call_status.dart';
import 'package:flutter_audio_room/features/instant_chat/domain/entities/match_status.dart';
import 'package:flutter_audio_room/features/instant_chat/presentation/provider/mixins/instant_call_web_socket_mixin.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/mixins/audio_stream_mixin.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/mixins/data_channel_mixin.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/mixins/encryption_mixin.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/mixins/ringtone_mixin.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/mixins/web_rtc_connection_mixin.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/voice_call_base.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/voice_call_state.dart';
import 'package:flutter_audio_room/services/crypto_service/i_crypto_service.dart';
import 'package:flutter_audio_room/services/session_key_service/i_session_key_service.dart';
import 'package:flutter_audio_room/services/web_rtc_service/i_web_rtc_service.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'instant_call_provider.g.dart';

@riverpod
class InstantCall extends _$InstantCall
    with 
        VoiceCallBaseMixin,
        AudioStreamMixin,
        InstantCallWebSocketMixin,
        WebRTCConnectionMixin,
        DataChannelMixin,
        EncryptionMixin,
        RingtoneMixin {
  @override
  VoiceCallState build() {
    LogUtils.d('build instant call provider', tag: '');
    audioPlayerController = ref.watch(audioPlayerControllerProvider.notifier);
    final myUserId = ref.read(accountProvider).userInfo?.profile?.id;
    if (myUserId == null) {
      return VoiceCallState(
        matchStatus: MatchError('User info not found'),
        callStatus: CallNotStarted(),
        myUserId: '',
      );
    }

    _callbackQueue = CallbackQueue();

    // initializeWebSocket();
  
    ref.onDispose(() async {
      // 清理基类资源
      dispose();
    });

    return VoiceCallState(
      matchStatus: const MatchNotStarted(),
      callStatus: CallNotStarted(),
      myUserId: myUserId,
    );
  }

  CallbackQueue? _callbackQueue;
  // CallBaseMixin 实现
  @override
  List<RTCRtpSender> senders = [];

  @override
  Map<String, FrameCryptor> frameCryptors = {};

  @override
  Map<String, int> cryptorRetryCount = {};

  @override
  IWebRtcService get webRtcService => getIt<IWebRtcService>();

  @override
  ISessionKeyService get sessionKeyService => getIt<ISessionKeyService>();

  @override
  ICryptoService get cryptoService => getIt<ICryptoService>();

  @override
  @protected
  CallbackQueue? get callbackQueue => _callbackQueue;

  // Error handling
  @override
  @protected
  void handleError(String message) {
    state = state.copyWith(
      callStatus: CallError(message),
      matchStatus: MatchError(message),
    );
  }

  @override
  Future<void> endCall() async {
    try {
      // 更新状态
      state = state.copyWith(
        callStatus: CallDisconnected(CallDisconnectReason.manualLeave),
        matchStatus: const MatchNotStarted(),
      );
    } catch (e) {
      LogUtils.e('Error ending call', tag: 'AudioStream', error: e);
    }
  }

  @override
  Future<void> dispose() async {
    LogUtils.d('dispose instant chat provider', tag: '');

    stopRingtone();

    // 先发送结束消息，确保在WebSocket关闭前发送
    await sendEndCallMessage();

    // 清理定时器
    disposeTimer();
    disposeMatchTimer(); // 清理匹配定时器

    // 清理WebSocket连接
    await disposeWebSocket();

    // 清理WebRTC相关资源
    await disposeWebRTC();

    // 清理加密相关资源
    await _disposeEncryption();

    // 清理其他资源
    callbackQueue?.clear();
  }

  /// 清理 WebRTC 相关资源
  @override
  @protected
  Future<void> disposeWebRTC() async {
    try {
      // 停止所有音频轨道 - 使用List.from创建副本避免并发修改
      final localTracks = List<MediaStreamTrack>.from(
          state.localStream?.getAudioTracks() ?? []);
      for (final track in localTracks) {
        track.enabled = false;
        await track.stop();
      }

      final remoteTracks =
          List<MediaStreamTrack>.from(
          state.remoteStream?.getAudioTracks() ?? []);
      for (final track in remoteTracks) {
        track.enabled = false;
        await track.stop();
      }

      // 关闭流
      await state.localStream?.dispose();
      await state.remoteStream?.dispose();

      // 关闭 DataChannel
      cleanUpDataChannel();

      // 关闭 PeerConnection
      await state.peerConnection?.close();

      state = state.copyWith(
        localStream: null,
        remoteStream: null,
        peerConnection: null,
      );
    } catch (e) {
      debugPrint('Error in disposeWebRTC: $e');
    }
  }

  /// 清理定时器相关资源
  @override
  @protected
  void disposeTimer() {
    durationTimer?.cancel();
    durationTimer = null;
    keyUpdateTimer?.cancel();
    keyUpdateTimer = null;
  }

  /// 清理加密相关资源
  @protected
  Future<void> _disposeEncryption() async {
    try {
      // 清理加密器
      // 创建一个副本，避免在迭代过程中修改集合
      final cryptorsCopy = List<FrameCryptor>.from(frameCryptors.values);
      for (var cryptor in cryptorsCopy) {
        await cryptor.dispose();
      }
      frameCryptors.clear();
      cryptorRetryCount.clear();

      // 清理密钥提供者
      await keyProvider?.dispose();
      keyProvider = null;
      sessionKey = null;
    } catch (e) {
      debugPrint('Error in _disposeEncryption: $e');
    }
  }
}
