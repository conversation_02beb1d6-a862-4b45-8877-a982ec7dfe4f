// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'instant_chat_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$instantChatHash() => r'bf9d9c7ed32cf1fe75ba0278e632ddbb9eb8d2bd';

/// See also [InstantChat].
@ProviderFor(InstantChat)
final instantChatProvider =
    AutoDisposeNotifierProvider<InstantChat, InstantChatState>.internal(
  InstantChat.new,
  name: r'instantChatProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$instantChatHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InstantChat = AutoDisposeNotifier<InstantChatState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
