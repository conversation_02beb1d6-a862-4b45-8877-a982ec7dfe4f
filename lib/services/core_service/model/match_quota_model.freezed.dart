// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'match_quota_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MatchQuotaModel _$MatchQuotaModelFromJson(Map<String, dynamic> json) {
  return _MatchQuotaModel.fromJson(json);
}

/// @nodoc
mixin _$MatchQuotaModel {
  int get instantVoiceCount => throw _privateConstructorUsedError;
  int get instantChatCount => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MatchQuotaModelCopyWith<MatchQuotaModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MatchQuotaModelCopyWith<$Res> {
  factory $MatchQuotaModelCopyWith(
          MatchQuotaModel value, $Res Function(MatchQuotaModel) then) =
      _$MatchQuotaModelCopyWithImpl<$Res, MatchQuotaModel>;
  @useResult
  $Res call({int instantVoiceCount, int instantChatCount});
}

/// @nodoc
class _$MatchQuotaModelCopyWithImpl<$Res, $Val extends MatchQuotaModel>
    implements $MatchQuotaModelCopyWith<$Res> {
  _$MatchQuotaModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? instantVoiceCount = null,
    Object? instantChatCount = null,
  }) {
    return _then(_value.copyWith(
      instantVoiceCount: null == instantVoiceCount
          ? _value.instantVoiceCount
          : instantVoiceCount // ignore: cast_nullable_to_non_nullable
              as int,
      instantChatCount: null == instantChatCount
          ? _value.instantChatCount
          : instantChatCount // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MatchQuotaModelImplCopyWith<$Res>
    implements $MatchQuotaModelCopyWith<$Res> {
  factory _$$MatchQuotaModelImplCopyWith(_$MatchQuotaModelImpl value,
          $Res Function(_$MatchQuotaModelImpl) then) =
      __$$MatchQuotaModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int instantVoiceCount, int instantChatCount});
}

/// @nodoc
class __$$MatchQuotaModelImplCopyWithImpl<$Res>
    extends _$MatchQuotaModelCopyWithImpl<$Res, _$MatchQuotaModelImpl>
    implements _$$MatchQuotaModelImplCopyWith<$Res> {
  __$$MatchQuotaModelImplCopyWithImpl(
      _$MatchQuotaModelImpl _value, $Res Function(_$MatchQuotaModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? instantVoiceCount = null,
    Object? instantChatCount = null,
  }) {
    return _then(_$MatchQuotaModelImpl(
      instantVoiceCount: null == instantVoiceCount
          ? _value.instantVoiceCount
          : instantVoiceCount // ignore: cast_nullable_to_non_nullable
              as int,
      instantChatCount: null == instantChatCount
          ? _value.instantChatCount
          : instantChatCount // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MatchQuotaModelImpl implements _MatchQuotaModel {
  const _$MatchQuotaModelImpl(
      {this.instantVoiceCount = 0, this.instantChatCount = 0});

  factory _$MatchQuotaModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$MatchQuotaModelImplFromJson(json);

  @override
  @JsonKey()
  final int instantVoiceCount;
  @override
  @JsonKey()
  final int instantChatCount;

  @override
  String toString() {
    return 'MatchQuotaModel(instantVoiceCount: $instantVoiceCount, instantChatCount: $instantChatCount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MatchQuotaModelImpl &&
            (identical(other.instantVoiceCount, instantVoiceCount) ||
                other.instantVoiceCount == instantVoiceCount) &&
            (identical(other.instantChatCount, instantChatCount) ||
                other.instantChatCount == instantChatCount));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, instantVoiceCount, instantChatCount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MatchQuotaModelImplCopyWith<_$MatchQuotaModelImpl> get copyWith =>
      __$$MatchQuotaModelImplCopyWithImpl<_$MatchQuotaModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MatchQuotaModelImplToJson(
      this,
    );
  }
}

abstract class _MatchQuotaModel implements MatchQuotaModel {
  const factory _MatchQuotaModel(
      {final int instantVoiceCount,
      final int instantChatCount}) = _$MatchQuotaModelImpl;

  factory _MatchQuotaModel.fromJson(Map<String, dynamic> json) =
      _$MatchQuotaModelImpl.fromJson;

  @override
  int get instantVoiceCount;
  @override
  int get instantChatCount;
  @override
  @JsonKey(ignore: true)
  _$$MatchQuotaModelImplCopyWith<_$MatchQuotaModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
