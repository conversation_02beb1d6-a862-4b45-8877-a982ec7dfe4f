import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/theme/text_styles.dart';
import 'package:flutter_audio_room/core/utils/app_screen_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_safe_area.dart';

class AppScaffold extends StatelessWidget {
  const AppScaffold({
    super.key,
    required this.body,
    this.presetSafeArea = true,
    this.appBar,
    this.backgroundColor,
    this.resizeToAvoidBottomInset = true,
    this.bottomNavigationBar,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.contentPadding,
  });

  final Widget body;
  final bool presetSafeArea;
  final PreferredSizeWidget? appBar;
  final Color? backgroundColor;
  final bool resizeToAvoidBottomInset;
  final Widget? bottomNavigationBar;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final EdgeInsets? contentPadding;

  @override
  Widget build(BuildContext context) {
    final bool isIOS = Platform.isIOS;
    Widget scaffoldBody = Padding(
      padding: contentPadding ??
          EdgeInsets.symmetric(horizontal: AppScreenUtils.setWidth(20)),
      child: body,
    );

    if (presetSafeArea) {
      scaffoldBody = AppSafeArea(
        child: scaffoldBody,
      );
    }

    CupertinoNavigationBar? iosAppBar;
    if (isIOS) {
      if (appBar is AppBar) {
        final adaptiveAppBar = appBar as AppBar;
        iosAppBar = CupertinoNavigationBar(
          padding: EdgeInsetsDirectional.zero,
          middle: adaptiveAppBar.title != null
              ? DefaultTextStyle(
                  style: AppTextStyles.headlineLargeBold.copyWith(
                    color: context.colorScheme.onSurface,
                  ),
                  child: adaptiveAppBar.title!,
                )
              : null,
          leading: adaptiveAppBar.leading,
          trailing: adaptiveAppBar.actions != null
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  children: adaptiveAppBar.actions!,
                )
              : null,
          backgroundColor:
              adaptiveAppBar.backgroundColor ?? context.colorScheme.surface,
          border: adaptiveAppBar.elevation != 0
              ? Border(
                  bottom: BorderSide(
                    color: context.colorScheme.onSurface.withValues(alpha: 0.3),
                    width: 0.0, // 0.0 means one physical pixel
                  ),
                )
              : const Border(bottom: BorderSide(color: Colors.transparent)),
        );
      }
    }

    return Scaffold(
      appBar: iosAppBar ?? appBar,
      body: scaffoldBody,
      backgroundColor:
          backgroundColor ?? Theme.of(context).scaffoldBackgroundColor,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      bottomNavigationBar: bottomNavigationBar,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
    );
  }
}
